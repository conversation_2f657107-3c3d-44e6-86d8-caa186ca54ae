package payment

import (
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha256"
	"crypto/sha512"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/google/uuid"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

// VNPayClient represents a VNPay payment client
type VNPayClient struct {
	BaseURL       string
	QRCreateURL   string
	CheckTransURL string
	RefundURL     string
	HTTPClient    *resty.Client
}

// VNPayQRCreateRequest represents a VNPay QR code creation request
type VNPayQRCreateRequest struct {
	MerchantCode string `json:"merchantCode"`
	MerchantName string `json:"merchantName"`
	MerchantType string `json:"merchantType"`
	TerminalId   string `json:"terminalId"`
	OrderId      string `json:"orderId"`
	Amount       int64  `json:"amount"`
	OrderInfo    string `json:"orderInfo"`
	AppId        string `json:"appId"`
	Signature    string `json:"signature"`
}

// VNPayQRCreateResponse represents a VNPay QR code creation response
type VNPayQRCreateResponse struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Data    struct {
		QRCode    string `json:"qrCode"`
		QRDataURL string `json:"qrDataURL"`
	} `json:"data"`
}

// VNPayCheckTransRequest represents a VNPay transaction check request
type VNPayCheckTransRequest struct {
	MerchantCode string `json:"merchantCode"`
	TerminalId   string `json:"terminalId"`
	OrderId      string `json:"orderId"`
	Signature    string `json:"signature"`
}

// VNPayCheckTransResponse represents a VNPay transaction check response
type VNPayCheckTransResponse struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Data    struct {
		OrderId       string `json:"orderId"`
		Amount        int64  `json:"amount"`
		Status        string `json:"status"`
		TransactionNo string `json:"transactionNo"`
		PayDate       string `json:"payDate"`
	} `json:"data"`
}

// VNPayRefundRequest represents a VNPay refund request
type VNPayRefundRequest struct {
	MerchantCode    string `json:"merchantCode"`
	TerminalId      string `json:"terminalId"`
	OrderId         string `json:"orderId"`
	TransactionNo   string `json:"transactionNo"`
	RefundAmount    int64  `json:"refundAmount"`
	RefundOrderId   string `json:"refundOrderId"`
	RefundOrderInfo string `json:"refundOrderInfo"`
	Signature       string `json:"signature"`
}

// VNPayRefundResponse represents a VNPay refund response
type VNPayRefundResponse struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Data    struct {
		RefundOrderId string `json:"refundOrderId"`
		RefundAmount  int64  `json:"refundAmount"`
		Status        string `json:"status"`
	} `json:"data"`
}

// Legacy VNPay structures for backward compatibility
type VNPayRequest struct {
	Version    string `json:"vnp_Version"`
	Command    string `json:"vnp_Command"`
	TmnCode    string `json:"vnp_TmnCode"`
	Amount     int64  `json:"vnp_Amount"`
	CurrCode   string `json:"vnp_CurrCode"`
	TxnRef     string `json:"vnp_TxnRef"`
	OrderInfo  string `json:"vnp_OrderInfo"`
	OrderType  string `json:"vnp_OrderType"`
	Locale     string `json:"vnp_Locale"`
	ReturnURL  string `json:"vnp_ReturnUrl"`
	IpnURL     string `json:"vnp_IpnUrl"`
	CreateDate string `json:"vnp_CreateDate"`
	ExpireDate string `json:"vnp_ExpireDate"`
	BankCode   string `json:"vnp_BankCode,omitempty"`
	SecureHash string `json:"vnp_SecureHash"`
}

type VNPayResponse struct {
	RspCode    string `json:"vnp_ResponseCode"`
	Message    string `json:"vnp_Message"`
	TmnCode    string `json:"vnp_TmnCode"`
	Amount     int64  `json:"vnp_Amount"`
	BankCode   string `json:"vnp_BankCode"`
	PayDate    string `json:"vnp_PayDate"`
	TxnRef     string `json:"vnp_TxnRef"`
	TransNo    string `json:"vnp_TransactionNo"`
	SecureHash string `json:"vnp_SecureHash"`
}

// VNPayQRResponse for backward compatibility
type VNPayQRResponse struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Data    struct {
		QRCode    string `json:"qrCode"`
		QRDataURL string `json:"qrDataURL"`
	} `json:"data"`
}

// NewVNPayClient creates a new VNPay payment client
func NewVNPayClient() *VNPayClient {
	return &VNPayClient{
		BaseURL:       "https://sandbox-payment.vnpay.vn", // Use production URL for live
		QRCreateURL:   "https://doitac-tran.vnpaytest.vn/QRCreateAPIRestV2/rest/CreateQrcodeApi/createQrcode",
		CheckTransURL: "https://doitac-tran.vnpaytest.vn/CheckTransaction/rest/api/CheckTrans",
		RefundURL:     "https://doitac-tran.vnpaytest.vn/mms/refund",
		HTTPClient:    resty.New(),
	}
}

// GetProviderName returns the provider name
func (c *VNPayClient) GetProviderName() string {
	return "vnpay"
}

// CreatePaymentLink creates a new VNPay payment transaction using QRCode API
func (c *VNPayClient) CreatePaymentLink(token *models.Token, request *PaymentRequest) (*PaymentResponse, error) {
	// Generate unique order ID
	orderID := c.generateOrderID(request.OrderID)

	// Create VNPay QR request using test credentials
	qrReq := VNPayQRCreateRequest{
		MerchantCode: "0318864354EV", // Test merchant code
		MerchantName: "EVERY TECH",   // Test merchant name
		MerchantType: "4513",         // Test merchant type
		TerminalId:   "EVETESTT",     // Test terminal ID
		OrderId:      orderID,
		Amount:       int64(request.Amount), // Amount in VND (no need to multiply by 100 for QR API)
		OrderInfo:    c.getOrderInfo(request.OrderID, request.Description),
		AppId:        "MERCHANT", // Test app ID
	}

	// Generate signature for QR creation
	signature := c.generateQRSignature(qrReq, "vnpay@MERCHANT")
	qrReq.Signature = signature

	// Send request to VNPay QR API
	resp, err := c.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetBody(qrReq).
		Post(c.QRCreateURL)

	if err != nil {
		return nil, fmt.Errorf("failed to create VNPay QR payment: %w", err)
	}

	// Parse response
	var qrResp VNPayQRCreateResponse
	if err := json.Unmarshal(resp.Body(), &qrResp); err != nil {
		return nil, fmt.Errorf("failed to parse VNPay QR response: %w", err)
	}

	// Check response code
	if qrResp.Code != "00" {
		return nil, fmt.Errorf("VNPay QR payment creation failed: %s", qrResp.Message)
	}

	return &PaymentResponse{
		Success:       true,
		TransactionID: orderID,
		PayURL:        qrResp.Data.QRDataURL, // Use QR data URL as payment URL
		QRCode:        qrResp.Data.QRCode,    // QR code content
		Message:       "VNPay QR payment created successfully",
		Data: map[string]interface{}{
			"orderId":      orderID,
			"amount":       qrReq.Amount,
			"orderInfo":    qrReq.OrderInfo,
			"qrCode":       qrResp.Data.QRCode,
			"qrDataURL":    qrResp.Data.QRDataURL,
			"merchantCode": qrReq.MerchantCode,
			"terminalId":   qrReq.TerminalId,
		},
	}, nil
}

// GetTransactionStatus retrieves VNPay transaction status using CheckTransaction API
func (c *VNPayClient) GetTransactionStatus(token *models.Token, transactionID string) (*TransactionStatus, error) {
	// Create check transaction request using test credentials
	checkReq := VNPayCheckTransRequest{
		MerchantCode: "0318864354EV", // Test merchant code
		TerminalId:   "EVETESTT",     // Test terminal ID
		OrderId:      transactionID,
	}

	// Generate signature for check transaction
	signature := c.generateCheckTransSignature(checkReq, "vnpay@123@langhaHangLa")
	checkReq.Signature = signature

	// Send request to VNPay CheckTransaction API
	resp, err := c.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetBody(checkReq).
		Post(c.CheckTransURL)

	if err != nil {
		return nil, fmt.Errorf("failed to check VNPay transaction status: %w", err)
	}

	// Parse response
	var checkResp VNPayCheckTransResponse
	if err := json.Unmarshal(resp.Body(), &checkResp); err != nil {
		return nil, fmt.Errorf("failed to parse VNPay check transaction response: %w", err)
	}

	// Map VNPay status to our status
	status := c.mapVNPayStatus(checkResp.Code)

	// Parse pay date if available
	var payDate time.Time
	if checkResp.Data.PayDate != "" {
		if parsedDate, err := time.Parse("**************", checkResp.Data.PayDate); err == nil {
			payDate = parsedDate
		}
	}

	return &TransactionStatus{
		TransactionID: transactionID,
		OrderID:       checkResp.Data.OrderId,
		Amount:        float64(checkResp.Data.Amount),
		Status:        status,
		Message:       checkResp.Message,
		CreatedAt:     payDate,
		UpdatedAt:     time.Now(),
		Data: map[string]interface{}{
			"transactionNo": checkResp.Data.TransactionNo,
			"status":        checkResp.Data.Status,
			"payDate":       checkResp.Data.PayDate,
			"code":          checkResp.Code,
		},
	}, nil
}

// ProcessCallback processes VNPay callback data
func (c *VNPayClient) ProcessCallback(data map[string]interface{}) (*CallbackResponse, error) {
	rspCode, _ := data["vnp_ResponseCode"].(string)
	txnRef, _ := data["vnp_TxnRef"].(string)
	amount, _ := data["vnp_Amount"].(string)
	transNo, _ := data["vnp_TransactionNo"].(string)

	// Convert amount from string to float
	amountFloat := 0.0
	if amount != "" {
		if amountInt, err := strconv.ParseInt(amount, 10, 64); err == nil {
			amountFloat = float64(amountInt) / 100 // Convert back from smallest unit
		}
	}

	// Map VNPay response code to status
	status := c.mapResponseCode(rspCode)
	success := rspCode == "00"

	return &CallbackResponse{
		Success:       success,
		TransactionID: txnRef,
		OrderID:       txnRef,
		Status:        status,
		Amount:        amountFloat,
		Message:       c.getResponseMessage(rspCode),
		Data: map[string]interface{}{
			"vnp_TransactionNo": transNo,
			"vnp_ResponseCode":  rspCode,
			"original_data":     data,
		},
	}, nil
}

// ValidateSignature validates VNPay callback signature
func (c *VNPayClient) ValidateSignature(token *models.Token, data map[string]interface{}, signature string) bool {
	// Remove signature from data for validation
	validationData := make(map[string]interface{})
	for k, v := range data {
		if k != "vnp_SecureHash" && k != "vnp_SecureHashType" {
			validationData[k] = v
		}
	}

	// Generate expected signature
	expectedSignature := c.generateSecureHashFromMap(validationData, token.Password)

	return strings.EqualFold(signature, expectedSignature)
}

// Helper methods
func (c *VNPayClient) generateTxnRef(orderID string) string {
	// VNPay transaction reference should be unique and alphanumeric
	timestamp := time.Now().Format("**************")
	return fmt.Sprintf("%s_%s", orderID, timestamp)
}

func (c *VNPayClient) getOrderInfo(orderID, description string) string {
	if description != "" {
		return description
	}
	return fmt.Sprintf("Thanh toan don hang %s", orderID)
}

func (c *VNPayClient) getLocale(language string) string {
	if language == "en" {
		return "en"
	}
	return "vn" // Default to Vietnamese
}

func (c *VNPayClient) getBankCode(paymentMethod string) string {
	// Map payment methods to VNPay bank codes
	switch paymentMethod {
	case "VNPAY_QR":
		return "VNPAYQR"
	case "VNBANK":
		return "VNBANK"
	case "INTCARD":
		return "INTCARD"
	case "VISA":
		return "VISA"
	case "MASTERCARD":
		return "MASTERCARD"
	case "JCB":
		return "JCB"
	default:
		return "" // Let user choose payment method
	}
}

func (c *VNPayClient) mapResponseCode(rspCode string) string {
	switch rspCode {
	case "00":
		return "COMPLETED"
	case "07":
		return "PENDING"
	case "09":
		return "CANCELLED"
	case "10", "11", "12", "13", "24", "51", "65", "75", "79", "99":
		return "FAILED"
	default:
		return "PENDING"
	}
}

func (c *VNPayClient) getResponseMessage(rspCode string) string {
	messages := map[string]string{
		"00": "Giao dịch thành công",
		"07": "Trừ tiền thành công. Giao dịch bị nghi ngờ (liên quan tới lừa đảo, giao dịch bất thường).",
		"09": "Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng chưa đăng ký dịch vụ InternetBanking tại ngân hàng.",
		"10": "Giao dịch không thành công do: Khách hàng xác thực thông tin thẻ/tài khoản không đúng quá 3 lần",
		"11": "Giao dịch không thành công do: Đã hết hạn chờ thanh toán. Xin quý khách vui lòng thực hiện lại giao dịch.",
		"12": "Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng bị khóa.",
		"13": "Giao dịch không thành công do Quý khách nhập sai mật khẩu xác thực giao dịch (OTP).",
		"24": "Giao dịch không thành công do: Khách hàng hủy giao dịch",
		"51": "Giao dịch không thành công do: Tài khoản của quý khách không đủ số dư để thực hiện giao dịch.",
		"65": "Giao dịch không thành công do: Tài khoản của Quý khách đã vượt quá hạn mức giao dịch trong ngày.",
		"75": "Ngân hàng thanh toán đang bảo trì.",
		"79": "Giao dịch không thành công do: KH nhập sai mật khẩu thanh toán quá số lần quy định.",
		"99": "Các lỗi khác (lỗi còn lại, không có trong danh sách mã lỗi đã liệt kê)",
	}

	if msg, exists := messages[rspCode]; exists {
		return msg
	}
	return "Lỗi không xác định"
}

func (c *VNPayClient) generateSecureHash(req VNPayRequest, secretKey string) string {
	// Create parameter map
	params := map[string]interface{}{
		"vnp_Version":    req.Version,
		"vnp_Command":    req.Command,
		"vnp_TmnCode":    req.TmnCode,
		"vnp_Amount":     req.Amount,
		"vnp_CurrCode":   req.CurrCode,
		"vnp_TxnRef":     req.TxnRef,
		"vnp_OrderInfo":  req.OrderInfo,
		"vnp_OrderType":  req.OrderType,
		"vnp_Locale":     req.Locale,
		"vnp_ReturnUrl":  req.ReturnURL,
		"vnp_IpnUrl":     req.IpnURL,
		"vnp_CreateDate": req.CreateDate,
		"vnp_ExpireDate": req.ExpireDate,
	}

	if req.BankCode != "" {
		params["vnp_BankCode"] = req.BankCode
	}

	return c.generateSecureHashFromMap(params, secretKey)
}

func (c *VNPayClient) generateSecureHashFromMap(params map[string]interface{}, secretKey string) string {
	// Sort parameters by key
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// Build query string
	var parts []string
	for _, key := range keys {
		value := fmt.Sprintf("%v", params[key])
		if value != "" {
			parts = append(parts, fmt.Sprintf("%s=%s", key, url.QueryEscape(value)))
		}
	}

	rawData := strings.Join(parts, "&")

	// Generate HMAC-SHA512 hash
	h := hmac.New(sha512.New, []byte(secretKey))
	h.Write([]byte(rawData))
	return strings.ToUpper(hex.EncodeToString(h.Sum(nil)))
}

func (c *VNPayClient) buildPaymentURL(req VNPayRequest) string {
	params := url.Values{}
	params.Set("vnp_Version", req.Version)
	params.Set("vnp_Command", req.Command)
	params.Set("vnp_TmnCode", req.TmnCode)
	params.Set("vnp_Amount", strconv.FormatInt(req.Amount, 10))
	params.Set("vnp_CurrCode", req.CurrCode)
	params.Set("vnp_TxnRef", req.TxnRef)
	params.Set("vnp_OrderInfo", req.OrderInfo)
	params.Set("vnp_OrderType", req.OrderType)
	params.Set("vnp_Locale", req.Locale)
	params.Set("vnp_ReturnUrl", req.ReturnURL)
	params.Set("vnp_IpnUrl", req.IpnURL)
	params.Set("vnp_CreateDate", req.CreateDate)
	params.Set("vnp_ExpireDate", req.ExpireDate)

	if req.BankCode != "" {
		params.Set("vnp_BankCode", req.BankCode)
	}

	params.Set("vnp_SecureHash", req.SecureHash)

	return fmt.Sprintf("%s/paymentv2/vpcpay.html?%s", c.BaseURL, params.Encode())
}

func (c *VNPayClient) generateQRCode(paymentURL string) string {
	// For VNPay QR code, you would typically:
	// 1. Use VNPay's QR API if available
	// 2. Generate QR code from payment URL using a QR library
	// 3. Return the QR code image URL or base64 data

	// This is a placeholder - in real implementation, you would generate actual QR code
	return fmt.Sprintf("https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=%s", url.QueryEscape(paymentURL))
}

// Helper methods for VNPay QRCode API

// generateOrderID creates a unique order ID for VNPay
func (c *VNPayClient) generateOrderID(originalOrderID string) string {
	// Generate unique order ID with timestamp
	timestamp := time.Now().Format("**************")
	uniqueID := strings.Split(uuid.New().String(), "-")[0]
	return fmt.Sprintf("%s_%s_%s", originalOrderID, timestamp, uniqueID)
}

// generateQRSignature generates signature for QR creation API
func (c *VNPayClient) generateQRSignature(req VNPayQRCreateRequest, secretKey string) string {
	// Create data string for signature according to VNPay QR API specification
	data := fmt.Sprintf("%s|%s|%s|%s|%s|%d|%s|%s",
		req.MerchantCode,
		req.MerchantName,
		req.MerchantType,
		req.TerminalId,
		req.OrderId,
		req.Amount,
		req.OrderInfo,
		req.AppId,
	)

	// Generate MD5 hash
	h := md5.New()
	h.Write([]byte(data + secretKey))
	return hex.EncodeToString(h.Sum(nil))
}

// generateCheckTransSignature generates signature for check transaction API
func (c *VNPayClient) generateCheckTransSignature(req VNPayCheckTransRequest, secretKey string) string {
	// Create data string for signature according to VNPay CheckTransaction API specification
	data := fmt.Sprintf("%s|%s|%s",
		req.MerchantCode,
		req.TerminalId,
		req.OrderId,
	)

	// Generate SHA256 hash
	h := sha256.New()
	h.Write([]byte(data + secretKey))
	return hex.EncodeToString(h.Sum(nil))
}

// mapVNPayStatus maps VNPay response codes to our standard status
func (c *VNPayClient) mapVNPayStatus(code string) string {
	switch code {
	case "00":
		return "COMPLETED"
	case "01", "02", "03":
		return "PENDING"
	case "04", "05", "06", "07", "08", "09":
		return "FAILED"
	default:
		return "PENDING"
	}
}

// RefundRequest represents a refund request
type RefundRequest struct {
	TransactionID string  `json:"transaction_id" binding:"required"`
	Amount        float64 `json:"amount" binding:"required"`
	Reason        string  `json:"reason,omitempty"`
}

// RefundResponse represents a refund response
type RefundResponse struct {
	Success       bool      `json:"success"`
	TransactionID string    `json:"transaction_id"`
	RefundID      string    `json:"refund_id"`
	Amount        float64   `json:"amount"`
	Status        string    `json:"status"` // PENDING, COMPLETED, FAILED
	Message       string    `json:"message,omitempty"`
	CreatedAt     time.Time `json:"created_at"`
	Data          any       `json:"data,omitempty"`
}

// Refund processes a refund for VNPay transaction using Refund API
func (c *VNPayClient) Refund(token *models.Token, request *RefundRequest) (*RefundResponse, error) {
	// Generate unique refund order ID
	refundOrderID := c.generateOrderID("REFUND_" + request.TransactionID)

	// Create VNPay refund request using test credentials
	refundReq := VNPayRefundRequest{
		MerchantCode:    "0318864354EV", // Test merchant code
		TerminalId:      "EVETESTT",     // Test terminal ID
		OrderId:         request.TransactionID,
		TransactionNo:   request.TransactionID, // Use transaction ID as transaction number
		RefundAmount:    int64(request.Amount),
		RefundOrderId:   refundOrderID,
		RefundOrderInfo: fmt.Sprintf("Refund for order %s", request.TransactionID),
	}

	// Generate signature for refund
	signature := c.generateRefundSignature(refundReq, "vnpayRefund")
	refundReq.Signature = signature

	// Send request to VNPay Refund API
	resp, err := c.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetBody(refundReq).
		Post(c.RefundURL)

	if err != nil {
		return nil, fmt.Errorf("failed to process VNPay refund: %w", err)
	}

	// Parse response
	var refundResp VNPayRefundResponse
	if err := json.Unmarshal(resp.Body(), &refundResp); err != nil {
		return nil, fmt.Errorf("failed to parse VNPay refund response: %w", err)
	}

	// Map response to our format
	success := refundResp.Code == "00"
	status := "FAILED"
	if success {
		status = "COMPLETED"
	}

	return &RefundResponse{
		Success:       success,
		TransactionID: request.TransactionID,
		RefundID:      refundResp.Data.RefundOrderId,
		Amount:        float64(refundResp.Data.RefundAmount),
		Status:        status,
		Message:       refundResp.Message,
		CreatedAt:     time.Now(),
		Data: map[string]interface{}{
			"refundOrderId": refundResp.Data.RefundOrderId,
			"refundAmount":  refundResp.Data.RefundAmount,
			"status":        refundResp.Data.Status,
			"code":          refundResp.Code,
		},
	}, nil
}

// generateRefundSignature generates signature for refund API
func (c *VNPayClient) generateRefundSignature(req VNPayRefundRequest, secretKey string) string {
	// Create data string for signature according to VNPay Refund API specification
	data := fmt.Sprintf("%s|%s|%s|%s|%d|%s|%s",
		req.MerchantCode,
		req.TerminalId,
		req.OrderId,
		req.TransactionNo,
		req.RefundAmount,
		req.RefundOrderId,
		req.RefundOrderInfo,
	)

	// Generate SHA256 hash
	h := sha256.New()
	h.Write([]byte(data + secretKey))
	return hex.EncodeToString(h.Sum(nil))
}
