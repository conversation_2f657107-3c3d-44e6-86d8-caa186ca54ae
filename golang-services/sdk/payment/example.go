package payment

import (
	"fmt"
	"log"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

// Example demonstrates how to use the generic payment system
func Example() {
	// Example token configuration
	token := &models.Token{
		SiteID:   "PARTNER123",
		Username: "test_username",
		Password: "test_password",
	}

	// Example payment request
	request := &PaymentRequest{
		OrderID:        "ORDER_12345",
		Amount:         100000, // 100,000 VND
		Description:    "Payment for order ORDER_12345",
		ClientCallback: "https://example.com/payment/success",
		ServerCallback: "https://example.com/api/payment/callback",
		PaymentMethod:  "MOMO", // For MOMO variants
		Language:       "vi",
	}

	// Example 1: Using MOMO payment
	fmt.Println("=== MOMO Payment Example ===")
	momoProvider := NewProvider("momo")
	if momoProvider != nil {
		response, err := momoProvider.CreatePaymentLink(token, request)
		if err != nil {
			log.Printf("MOMO payment creation failed: %v", err)
		} else {
			fmt.Printf("MOMO Payment created successfully:\n")
			fmt.Printf("  Transaction ID: %s\n", response.TransactionID)
			fmt.Printf("  Pay URL: %s\n", response.PayURL)
			fmt.Printf("  QR Code: %s\n", response.QRCode)
			fmt.Printf("  Deeplink: %s\n", response.Deeplink)
		}

		// Check transaction status
		if response != nil && response.TransactionID != "" {
			status, err := momoProvider.GetTransactionStatus(token, response.TransactionID)
			if err != nil {
				log.Printf("Failed to get MOMO transaction status: %v", err)
			} else {
				fmt.Printf("MOMO Transaction Status: %s\n", status.Status)
			}
		}
	}

	// Example 2: Using NEXDORPay payment
	fmt.Println("\n=== NEXDORPay Payment Example ===")
	nexdorpayProvider := NewProvider("nexdorpay")
	if nexdorpayProvider != nil {
		response, err := nexdorpayProvider.CreatePaymentLink(token, request)
		if err != nil {
			log.Printf("NEXDORPay payment creation failed: %v", err)
		} else {
			fmt.Printf("NEXDORPay Payment created successfully:\n")
			fmt.Printf("  Transaction ID: %s\n", response.TransactionID)
			fmt.Printf("  Pay URL: %s\n", response.PayURL)
			fmt.Printf("  QR Code: %s\n", response.QRCode)
		}

		// Check transaction status
		if response != nil && response.TransactionID != "" {
			status, err := nexdorpayProvider.GetTransactionStatus(token, response.TransactionID)
			if err != nil {
				log.Printf("Failed to get NEXDORPay transaction status: %v", err)
			} else {
				fmt.Printf("NEXDORPay Transaction Status: %s\n", status.Status)
			}
		}
	}

	// Example 3: Using PayOS payment
	fmt.Println("\n=== PayOS Payment Example ===")
	payosProvider := NewProvider("payos")
	if payosProvider != nil {
		response, err := payosProvider.CreatePaymentLink(token, request)
		if err != nil {
			log.Printf("PayOS payment creation failed: %v", err)
		} else {
			fmt.Printf("PayOS Payment created successfully:\n")
			fmt.Printf("  Transaction ID: %s\n", response.TransactionID)
			fmt.Printf("  Pay URL: %s\n", response.PayURL)
			fmt.Printf("  QR Code: %s\n", response.QRCode)
		}

		// Check transaction status
		if response != nil && response.TransactionID != "" {
			status, err := payosProvider.GetTransactionStatus(token, response.TransactionID)
			if err != nil {
				log.Printf("Failed to get PayOS transaction status: %v", err)
			} else {
				fmt.Printf("PayOS Transaction Status: %s\n", status.Status)
			}
		}
	}

	// Example 4: Using VNPay payment
	fmt.Println("\n=== VNPay Payment Example ===")
	vnpayProvider := NewProvider("vnpay")
	if vnpayProvider != nil {
		// VNPay specific request with QR code
		vnpayRequest := &PaymentRequest{
			OrderID:        "ORDER_12345",
			Amount:         100000, // 100,000 VND
			Description:    "Payment for order ORDER_12345",
			ClientCallback: "https://example.com/payment/success",
			ServerCallback: "https://example.com/api/payment/callback",
			PaymentMethod:  "VNPAY_QR", // VNPay QR code
			Language:       "vi",
		}

		response, err := vnpayProvider.CreatePaymentLink(token, vnpayRequest)
		if err != nil {
			log.Printf("VNPay payment creation failed: %v", err)
		} else {
			fmt.Printf("VNPay Payment created successfully:\n")
			fmt.Printf("  Transaction ID: %s\n", response.TransactionID)
			fmt.Printf("  Pay URL: %s\n", response.PayURL)
			fmt.Printf("  QR Code: %s\n", response.QRCode)
		}

		// Check transaction status
		if response != nil && response.TransactionID != "" {
			status, err := vnpayProvider.GetTransactionStatus(token, response.TransactionID)
			if err != nil {
				log.Printf("Failed to get VNPay transaction status: %v", err)
			} else {
				fmt.Printf("VNPay Transaction Status: %s\n", status.Status)
			}
		}
	}

	// Example 5: Callback Processing Examples
	fmt.Println("\n=== Callback Processing Example ===")

	// MOMO callback example
	momoCallbackData := map[string]interface{}{
		"orderId":    "ORDER_12345_abc123",
		"resultCode": float64(0), // Success
		"amount":     float64(100000),
		"message":    "Payment successful",
	}

	if momoProvider != nil {
		callbackResp, err := momoProvider.ProcessCallback(momoCallbackData)
		if err != nil {
			log.Printf("Failed to process MOMO callback: %v", err)
		} else {
			fmt.Printf("MOMO Callback processed:\n")
			fmt.Printf("  Success: %t\n", callbackResp.Success)
			fmt.Printf("  Status: %s\n", callbackResp.Status)
			fmt.Printf("  Amount: %.0f\n", callbackResp.Amount)
		}
	}

	// VNPay callback example
	vnpayCallbackData := map[string]interface{}{
		"vnp_ResponseCode":  "00",
		"vnp_TxnRef":        "ORDER_12345_20240101120000",
		"vnp_Amount":        "10000000", // 100,000 VND * 100
		"vnp_TransactionNo": "14123456",
		"vnp_SecureHash":    "sample_hash",
	}

	if vnpayProvider != nil {
		callbackResp, err := vnpayProvider.ProcessCallback(vnpayCallbackData)
		if err != nil {
			log.Printf("Failed to process VNPay callback: %v", err)
		} else {
			fmt.Printf("VNPay Callback processed:\n")
			fmt.Printf("  Success: %t\n", callbackResp.Success)
			fmt.Printf("  Status: %s\n", callbackResp.Status)
			fmt.Printf("  Amount: %.0f\n", callbackResp.Amount)
			fmt.Printf("  Message: %s\n", callbackResp.Message)
		}
	}

	// NEXDORPay callback example
	nexdorpayCallbackData := map[string]interface{}{
		"transaction_id": "abc123def456",
		"status":         "SETTLED",
		"amount":         float64(100000),
		"message":        "Payment completed",
	}

	if nexdorpayProvider != nil {
		callbackResp, err := nexdorpayProvider.ProcessCallback(nexdorpayCallbackData)
		if err != nil {
			log.Printf("Failed to process NEXDORPay callback: %v", err)
		} else {
			fmt.Printf("NEXDORPay Callback processed:\n")
			fmt.Printf("  Success: %t\n", callbackResp.Success)
			fmt.Printf("  Status: %s\n", callbackResp.Status)
			fmt.Printf("  Amount: %.0f\n", callbackResp.Amount)
		}
	}

	// PayOS callback example
	payosCallbackData := map[string]interface{}{
		"code": "00",
		"data": map[string]interface{}{
			"paymentLinkId": "payment_link_123",
			"orderCode":     float64(123456789),
			"amount":        float64(100000),
		},
	}

	if payosProvider != nil {
		callbackResp, err := payosProvider.ProcessCallback(payosCallbackData)
		if err != nil {
			log.Printf("Failed to process PayOS callback: %v", err)
		} else {
			fmt.Printf("PayOS Callback processed:\n")
			fmt.Printf("  Success: %t\n", callbackResp.Success)
			fmt.Printf("  Status: %s\n", callbackResp.Status)
			fmt.Printf("  Amount: %.0f\n", callbackResp.Amount)
		}
	}
}

// ExampleUsageInService demonstrates how to use the payment system in a service
func ExampleUsageInService(vendor string, token *models.Token, orderID string, amount float64) (*PaymentResponse, error) {
	// Create payment provider
	provider := NewProvider(vendor)
	if provider == nil {
		return nil, fmt.Errorf("unsupported payment provider: %s", vendor)
	}

	// Create payment request
	request := &PaymentRequest{
		OrderID:        orderID,
		Amount:         amount,
		Description:    fmt.Sprintf("Payment for order %s", orderID),
		ClientCallback: "https://yourapp.com/payment/success",
		ServerCallback: "https://yourapp.com/api/payment/callback",
		PaymentMethod:  getDefaultPaymentMethod(vendor),
		Language:       "vi",
	}

	// Create payment link
	response, err := provider.CreatePaymentLink(token, request)
	if err != nil {
		return nil, fmt.Errorf("failed to create payment link: %w", err)
	}

	return response, nil
}

// ExampleCallbackHandler demonstrates how to handle payment callbacks
func ExampleCallbackHandler(vendor string, callbackData map[string]interface{}) (*CallbackResponse, error) {
	// Create payment provider
	provider := NewProvider(vendor)
	if provider == nil {
		return nil, fmt.Errorf("unsupported payment provider: %s", vendor)
	}

	// Process callback
	response, err := provider.ProcessCallback(callbackData)
	if err != nil {
		return nil, fmt.Errorf("failed to process callback: %w", err)
	}

	return response, nil
}

// Helper function to get default payment method for each vendor
func getDefaultPaymentMethod(vendor string) string {
	switch vendor {
	case "momo":
		return "MOMO"
	case "vnpay":
		return "VNPAY_QR"
	case "nexdorpay":
		return ""
	case "payos":
		return ""
	default:
		return ""
	}
}

// VNPayQRCodeDirectExample demonstrates direct usage of VNPay QRCode client
func VNPayQRCodeDirectExample() {
	fmt.Println("=== VNPay QRCode Direct Client Example ===")

	// Create VNPay client directly
	vnpayClient := NewVNPayClient()

	// Create token with test credentials for QR creation
	token := &models.Token{
		SiteID:   "0318864354EV",   // Test merchant code
		Password: "vnpay@MERCHANT", // Test secret key for QR creation
	}

	// Create payment request
	paymentRequest := &PaymentRequest{
		OrderID:        "DIRECT_ORDER_001",
		Amount:         150000, // 150,000 VND
		Currency:       "VND",
		Description:    "Direct VNPay QRCode payment test",
		ClientCallback: "https://your-app.com/payment/callback",
		ServerCallback: "https://your-app.com/payment/webhook",
		PaymentMethod:  "VNPAY_QR",
		Language:       "vn",
	}

	// Create QRCode payment
	response, err := vnpayClient.CreatePaymentLink(token, paymentRequest)
	if err != nil {
		log.Printf("Error creating VNPay QRCode payment: %v", err)
		return
	}

	fmt.Printf("VNPay QRCode Payment created successfully:\n")
	fmt.Printf("  Transaction ID: %s\n", response.TransactionID)
	fmt.Printf("  QR Code: %s\n", response.QRCode)
	fmt.Printf("  Payment URL: %s\n", response.PayURL)
	fmt.Printf("  Success: %t\n", response.Success)
	fmt.Printf("  Message: %s\n", response.Message)

	// Check transaction status using different credentials
	checkToken := &models.Token{
		SiteID:   "0318864354EV",
		Password: "vnpay@123@langhaHangLa", // Test secret key for check transaction
	}

	status, err := vnpayClient.GetTransactionStatus(checkToken, response.TransactionID)
	if err != nil {
		log.Printf("Error checking VNPay transaction status: %v", err)
		return
	}

	fmt.Printf("VNPay Transaction Status:\n")
	fmt.Printf("  Transaction ID: %s\n", status.TransactionID)
	fmt.Printf("  Order ID: %s\n", status.OrderID)
	fmt.Printf("  Amount: %.2f\n", status.Amount)
	fmt.Printf("  Status: %s\n", status.Status)
	fmt.Printf("  Message: %s\n", status.Message)

	// Example refund (if needed)
	if status.Status == "COMPLETED" {
		refundRequest := &RefundRequest{
			TransactionID: response.TransactionID,
			Amount:        75000, // Partial refund
			Reason:        "Customer requested partial refund",
		}

		refundToken := &models.Token{
			SiteID:   "0318864354EV",
			Password: "vnpayRefund", // Test secret key for refund
		}

		refundResponse, err := vnpayClient.Refund(refundToken, refundRequest)
		if err != nil {
			log.Printf("Error processing VNPay refund: %v", err)
		} else {
			fmt.Printf("VNPay Refund processed:\n")
			fmt.Printf("  Success: %t\n", refundResponse.Success)
			fmt.Printf("  Refund ID: %s\n", refundResponse.RefundID)
			fmt.Printf("  Amount: %.2f\n", refundResponse.Amount)
			fmt.Printf("  Status: %s\n", refundResponse.Status)
		}
	}
}
