package payment

import (
	"testing"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

func TestVNPayQRCodeClient(t *testing.T) {
	// Test VNPay client creation
	client := NewVNPayClient()
	if client == nil {
		t.Fatal("Failed to create VNPay client")
	}

	// Verify URLs are set correctly
	expectedQRURL := "https://doitac-tran.vnpaytest.vn/QRCreateAPIRestV2/rest/CreateQrcodeApi/createQrcode"
	if client.QRCreateURL != expectedQRURL {
		t.Errorf("Expected QRCreateURL to be %s, got %s", expectedQRURL, client.QRCreateURL)
	}

	expectedCheckURL := "https://doitac-tran.vnpaytest.vn/CheckTransaction/rest/api/CheckTrans"
	if client.CheckTransURL != expectedCheckURL {
		t.Errorf("Expected CheckTransURL to be %s, got %s", expectedCheckURL, client.CheckTransURL)
	}

	expectedRefundURL := "https://doitac-tran.vnpaytest.vn/mms/refund"
	if client.RefundURL != expectedRefundURL {
		t.Errorf("Expected RefundURL to be %s, got %s", expectedRefundURL, client.RefundURL)
	}
}

func TestVNPaySignatureGeneration(t *testing.T) {
	client := NewVNPayClient()

	// Test QR signature generation
	qrReq := VNPayQRCreateRequest{
		MerchantCode: "0318864354EV",
		MerchantName: "EVERY TECH",
		MerchantType: "4513",
		TerminalId:   "EVETESTT",
		OrderId:      "TEST_ORDER_001",
		Amount:       100000,
		OrderInfo:    "Test payment",
		AppId:        "MERCHANT",
	}

	signature := client.generateQRSignature(qrReq, "vnpay@MERCHANT")
	if signature == "" {
		t.Error("QR signature should not be empty")
	}

	// Test check transaction signature generation
	checkReq := VNPayCheckTransRequest{
		MerchantCode: "0318864354EV",
		TerminalId:   "EVETESTT",
		OrderId:      "TEST_ORDER_001",
	}

	checkSignature := client.generateCheckTransSignature(checkReq, "vnpay@123@langhaHangLa")
	if checkSignature == "" {
		t.Error("Check transaction signature should not be empty")
	}

	// Test refund signature generation
	refundReq := VNPayRefundRequest{
		MerchantCode:    "0318864354EV",
		TerminalId:      "EVETESTT",
		OrderId:         "TEST_ORDER_001",
		TransactionNo:   "TEST_TRANS_001",
		RefundAmount:    50000,
		RefundOrderId:   "REFUND_001",
		RefundOrderInfo: "Test refund",
	}

	refundSignature := client.generateRefundSignature(refundReq, "vnpayRefund")
	if refundSignature == "" {
		t.Error("Refund signature should not be empty")
	}
}

func TestVNPayStatusMapping(t *testing.T) {
	client := NewVNPayClient()

	testCases := []struct {
		code     string
		expected string
	}{
		{"00", "COMPLETED"},
		{"01", "PENDING"},
		{"02", "PENDING"},
		{"03", "PENDING"},
		{"04", "FAILED"},
		{"05", "FAILED"},
		{"06", "FAILED"},
		{"07", "FAILED"},
		{"08", "FAILED"},
		{"09", "FAILED"},
		{"99", "PENDING"}, // Unknown code should default to PENDING
	}

	for _, tc := range testCases {
		result := client.mapVNPayStatus(tc.code)
		if result != tc.expected {
			t.Errorf("Expected status for code %s to be %s, got %s", tc.code, tc.expected, result)
		}
	}
}

func TestVNPayOrderIDGeneration(t *testing.T) {
	client := NewVNPayClient()

	orderID1 := client.generateOrderID("ORDER_001")
	orderID2 := client.generateOrderID("ORDER_001")

	// Order IDs should be unique
	if orderID1 == orderID2 {
		t.Error("Generated order IDs should be unique")
	}

	// Order IDs should contain the original order ID
	if len(orderID1) <= len("ORDER_001") {
		t.Error("Generated order ID should be longer than original")
	}
}

func TestVNPayHelperMethods(t *testing.T) {
	client := NewVNPayClient()

	// Test getOrderInfo
	orderInfo := client.getOrderInfo("ORDER_001", "Test payment")
	if orderInfo == "" {
		t.Error("Order info should not be empty")
	}

	// Test getLocale
	locale := client.getLocale("vi")
	if locale != "vn" {
		t.Errorf("Expected locale to be 'vn', got '%s'", locale)
	}

	locale = client.getLocale("en")
	if locale != "en" {
		t.Errorf("Expected locale to be 'en', got '%s'", locale)
	}
}

func TestVNPayPaymentRequest(t *testing.T) {
	// Test payment request structure
	request := &PaymentRequest{
		OrderID:        "TEST_ORDER_001",
		Amount:         100000,
		Currency:       "VND",
		Description:    "Test payment for VNPay QRCode",
		ClientCallback: "https://test.com/callback",
		ServerCallback: "https://test.com/webhook",
		PaymentMethod:  "VNPAY_QR",
		Language:       "vn",
	}

	if request.OrderID == "" {
		t.Error("Order ID should not be empty")
	}

	if request.Amount <= 0 {
		t.Error("Amount should be greater than 0")
	}

	if request.PaymentMethod != "VNPAY_QR" {
		t.Error("Payment method should be VNPAY_QR")
	}
}

func TestVNPayToken(t *testing.T) {
	// Test token structure for different VNPay operations
	qrToken := &models.Token{
		SiteID:   "0318864354EV",
		Password: "vnpay@MERCHANT",
	}

	checkToken := &models.Token{
		SiteID:   "0318864354EV",
		Password: "vnpay@123@langhaHangLa",
	}

	refundToken := &models.Token{
		SiteID:   "0318864354EV",
		Password: "vnpayRefund",
	}

	if qrToken.SiteID != "0318864354EV" {
		t.Error("QR token site ID should match test merchant code")
	}

	if checkToken.Password != "vnpay@123@langhaHangLa" {
		t.Error("Check token password should match test secret key")
	}

	if refundToken.Password != "vnpayRefund" {
		t.Error("Refund token password should match test refund secret key")
	}
}

func TestVNPayResponseStructures(t *testing.T) {
	// Test QR create response
	qrResp := VNPayQRCreateResponse{
		Code:    "00",
		Message: "Success",
		Data: struct {
			QRCode    string `json:"qrCode"`
			QRDataURL string `json:"qrDataURL"`
		}{
			QRCode:    "test_qr_code",
			QRDataURL: "https://test.com/qr",
		},
	}

	if qrResp.Code != "00" {
		t.Error("QR response code should be '00'")
	}

	if qrResp.Data.QRCode == "" {
		t.Error("QR code should not be empty")
	}

	// Test check transaction response
	checkResp := VNPayCheckTransResponse{
		Code:    "00",
		Message: "Success",
		Data: struct {
			OrderId       string `json:"orderId"`
			Amount        int64  `json:"amount"`
			Status        string `json:"status"`
			TransactionNo string `json:"transactionNo"`
			PayDate       string `json:"payDate"`
		}{
			OrderId:       "TEST_ORDER_001",
			Amount:        100000,
			Status:        "COMPLETED",
			TransactionNo: "TEST_TRANS_001",
			PayDate:       "20231201120000",
		},
	}

	if checkResp.Data.Amount != 100000 {
		t.Error("Check response amount should be 100000")
	}

	// Test refund response
	refundResp := VNPayRefundResponse{
		Code:    "00",
		Message: "Success",
		Data: struct {
			RefundOrderId string `json:"refundOrderId"`
			RefundAmount  int64  `json:"refundAmount"`
			Status        string `json:"status"`
		}{
			RefundOrderId: "REFUND_001",
			RefundAmount:  50000,
			Status:        "COMPLETED",
		},
	}

	if refundResp.Data.RefundAmount != 50000 {
		t.Error("Refund response amount should be 50000")
	}
}
