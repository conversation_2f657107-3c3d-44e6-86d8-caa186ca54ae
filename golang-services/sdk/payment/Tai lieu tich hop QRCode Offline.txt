    CÔNG TY CỔ PHẦN GIẢI PHÁP THANH TOÁN VIỆT NAM




                    Simplify the life




         HỆ THỐNG QR MMS VNPAY

    TÀI LIỆU ĐẶC TẢ KẾT NỐI
               MERCHANT
                   Phiên bản: 1.0.2




1
                                             Bảng ghi nhận thay đổi tài liệu

      Ngày thay đổi    Vị trí thay đổi            Lý do            Người sửa      Phiên      Mô tả thay đổi    Phiên
                                                                                  bản cũ                      bản mới

                      DEV                Update                 Hungnv1          1.0.0     Thêm API           1.0.1
                                                                                           checkTransaction
                                         API checkTransaction

    12/06/2024        HTKT               Update                 Trần Văn Ngọc   1.0.1      Thêm code gen QR 1.0.2
                                                                                           theo tiêu chuẩn
                                                                                           VNPAY




2
                             Trang ký



    Người lập: ………………………………………………… Ngày…………………..




    Người xem xét: …………………………………………… Ngày …………………..




    Người xem xét: ……………………………………………Ngày…………………..




    Người xem xét: ……………………………………………Ngày…………………..




    Người phê duyệt: ………………………………………… Ngày…………………..




    Người phê duyệt: ………………………………………… Ngày…………………..




    Người phê duyệt: ………………………………………… Ngày…………………..




3
                                                                      MỤC LỤC



    1     GIỚI THIỆU........................................................................................................................................................5
        1.1      Mục đích tài liệu ..........................................................................................................................................5
        1.2      Phạm vi tài liệu ............................................................................................................................................5
        1.3      Đối tượng sử dụng tài liệu ...........................................................................................................................5
        1.4      Kết quả mong muốn sau triển khai ..............................................................................................................5
        1.5      Định nghĩa thuật ngữ và các từ viết tắt ........................................................................................................5
        1.6      Tài liệu tham khảo .......................................................................................................................................5
    2     MÔ HÌNH KẾT NỐI ...........................................................................................................................................6
        2.1      Mô hình kết nối VNPAY - Merchant ..........................................................................................................6
        2.2      Giao thức kết nối .........................................................................................................................................6
    3     MÔ TẢ LUỒNG GIAO DỊCH............................................................................................................................7
        3.1      Mô tả luồng giao dịch thanh toán cho Merchant có kết nối hệ thống. ........................................................7
        3.2      Use Case ......................................................................................................................................................8
    4     ĐẶC TẢ KẾT NỐI GIỮA VNPAY-MERCHANT ..........................................................................................16
        4.1      Yêu cầu khởi tạo QRCode (CreateQRCode) .............................................................................................17
        4.2      Yêu cầu cập nhật trạng thái thanh toán (Merchant Payment)....................................................................22
        4.3      Yêu cầu kiểm tra giao dịch (CheckTrans) .................................................................................................26
        4.4      Yêu cầu hoàn tiền (MerchantRefund) .......................................................................................................28




4
    1 GIỚI THIỆU
    1.1    Mục đích tài liệu
           Tài liệu này đặc tả kết nối giữa hệ thống MMS của VNPAY với hệ thống của Merchant
           nhằm mục đích thực hiện giao dịch thanh toán QR.
    1.2    Phạm vi tài liệu
           Tài liệu cung cấp các thông tin chi tiết cần thiết để hệ thống MMS tại VNPAY có thể
           giao tiếp với hệ thống của Merchant.
           Tài liệu này mô tả định dạng trao đổi giữa VNPAY và Merchant cho những giao dịch
           dưới đây:
    1.3    Đối tượng sử dụng tài liệu
           Đối tượng sử dụng tài liệu là các cán bộ kỹ thuật và đối tác trực tiếp xây dựng giao diện
           kết nối của VNPAY và Merchant.
    1.4    Kết quả mong muốn sau triển khai
           Thực hiện kết nối thành công đúng theo đặc tả đề ra.
    1.5    Định nghĩa thuật ngữ và các từ viết tắt

             Thuật ngữ                               Định nghĩa                           Ghi chú
          VNPAY               Công ty cổ phần giải pháp thanh toán Việt Nam
          Merchant            Đơn vị chấp nhận thanh toán QR
          MMS                 Hệ thống quản trị Merchant của VNPAY
          Merchant Site       Giao diện website hỗ trợ Merchant tra cứu, theo dõi, báo
                              cáo giao dịch QR
          Merchant Apps       Giao diện ứng dụng Mobile nhận notification, tra cứu giao
                              dịch QR
          Merchant Server     Hệ thống Merchant kết nối với hệ thống MMS VNPAY
          Terminal            Các điểm chấp nhận thanh toán của Merchant
          Mobile              Hệ thống Mobile Banking hỗ trợ khách hàng thực hiện
                              giao dịch thanh toán QR

    1.6    Tài liệu tham khảo




5
    2 MÔ HÌNH KẾT NỐI
    2.1   Mô hình kết nối VNPAY - Merchant




    2.2   Giao thức kết nối

           Chức năng                                          Ghi chú
     Truyền thông             Sử dụng kênh truyền riêng ( leased-line) hoặc Interhet
     Giao thức kết nối        RESTful

     Tiêu chuẩn kết nối
     Bảo mật đường truyền
     dữ liệu
     Vai trò kết nối          - VNPAY cài đặt 1 cổng truyền dữ liệu sang hệ thống Merchant.
                              - Merchant cài đặt 1 cổng Payment Gateway tiếp nhận dịch vụ từ phía
                                VNPAY.
                              - Merchant xử lý giao dịch và trả về đúng VNPAY gọi tới.




6
    3 MÔ TẢ LUỒNG GIAO DỊCH
      3.1   Mô tả luồng giao dịch thanh toán cho Merchant có kết nối hệ thống.
            - Sơ đồ giao dịch:




            -     Mô tả:

                o Bước 1: Hệ thống Merchant gửi yêu cầu tạo QR (gọi API CreateQRCode) sang
                   hệ thống MMS VNPAY để tạo QR và thể hiện mã trên giao diện của hệ thống
                   Merchant:
                o Bước 2: Khách hàng sử dụng chức năng QR Pay trên ứng dụng Mobile Banking
                   để thanh toán cho Merchant.
                o Bước 3->9: Ngân hàng thực hiện xác thực thông tin giao dịch của KH và thực
                   hiện trừ tiền KH.



7
          o Bước 10: Hệ thống Ngân hàng thông báo kết quả trừ tiền khách hàng và gửi yêu
              cầu thanh toán sang hệ thống MMS VNPAY.
          o Bước 11: Hệ thống MMS VNPAY nhận được kết quả từ tiền từ Ngân hàng tiến
              hành xử lý:
                  ▪   Nếu kết quả trừ tiền thành công thì thực hiện thông báo kết quả cho
                      Merchant để tiến hành xử lý đơn hàng (gọi API Payment
                      MerchantGateway ở bước 12.1).
                  ▪   Nếu kết quả trừ tiền không thành công, hệ thống MMS tiến hành cập
                      nhật trạng thái giao dịch để hỗ trợ chăm sóc khách hàng.
          o Bước 12: Merchant tiến hành xử lý đơn hàng và giao hàng cho Khách hàng, trả
              kết quả cho hệ thống MMS VNPAY.
          o Bước 13: Hệ thống MMS VNPAY nhận được kết quả giao hàng, tiến hành cập
              nhật trạng thái:
                  ▪   Nếu kết quả giao hàng thành công thì cập nhật trạng thái giao hàng
                      thành công và kết thúc quy trình.
                  ▪   Nếu kết quả giao hàng không thành công thì tiến hành gọi sang hệ
                      thống MB để đảo giao dịch hoàn tiền cho khách hàng.

    3.2   Use Case
    3.2.1 Thanh toán QR Code điểm chấp nhận offline (Tĩnh, động)
       Mục đích: Khách hàng thanh toán thành cho mã QR được in sẵn cho điểm bán hoặc cho các
       hóa đơn in sẵn tại các điếm bán qua chức năng QR Pay trên các ứng dụng Mobile Banking.
       VD: QR cho cho Taxi, nhà hàng,…

       Các Ngân hàng hỗ trợ thanh toán: Agribank, Vietcombank, Vietinbank, BIDV, SCB, IVB,
       NCB, SHB, MSB, VIB
       Các bước thực hiện:

    ● Bước 1:

          o QR Tĩnh: Merchant thực hiện dán mã QR tại các quầy thu ngân điểm bán
            hàng chấp nhận thanh toán QR:




8
           o QR Động: Merchant có các hệ thống quản lý bán hàng tại thiết bị quầy thu
             ngân hay ứng dụng hiển thị QR có số tiền khách hàng cần thanh toán.

    ● Bước 2:

           ▪   Đăng nhập vào App Mobile Banking có hỗ trợ QR Code thanh toán
           ▪   Tại màn hình Home, chọn chức năng QR Pay




    ● Bước 3:
           ▪   Cách 1: Thực hiện di chuyển camera đến vùng chứa mã QR điểm bán

           ▪   Cách 2: Chọn mã QR từ thư viện ảnh đã lưu bằng cách nhấn vào icon        ,
               sau đó chọn một ảnh trong album kho ảnh của thiết bị.
           Nếu mã QR hợp lệ di chuyển sang bước 4
    ● Bước 4. Ứng dụng hiển thị màn hình xác nhận thông tin thanh toán.
       Nhấn nút Thanh toán




9
        Khách hàng thực hiện xác thực giao dịch và Nhấn nút Tiếp tục để hoàn tất.
     ● Bước 5. Ứng dụng hiển thị màn hình giao dịch thành công.




     3.2.2 Thanh toán QR Code sản phẩm
        Mục đích: Khách hàng thanh toán thành công cho mã QR được in sẵn trên hàng hóa, sản
        phẩm qua chức năng QR Pay trên các ứng dụng Mobile Banking.
        Ví dụ: QR trên các Catalogue, Siêu thị ảo
        Các Ngân hàng hỗ trợ thanh toán như: Agribank, Vietcombank, Vietinbank, BIDV, SCB,
        IVB, NCB, SHB, MSB, VIB
        Các bước thực hiện:

     ● Bước 1: Merchant thực hiện dán QR cho các sản phẩm tại các siêu thị ảo:




10
     ● Bước 2:

           ▪   Khách hàng đăng nhập vào App Mobile Banking có hỗ trợ QR Code thanh toán
           ▪   Tại màn hình Home, chọn chức năng QR Pay




     ● Bước 3:
           ▪   Cách 1: Thực hiện di chuyển camera đến vùng chứa mã QR sản phẩm

           ▪   Cách 2: Chọn mã QR từ thư viện ảnh đã lưu bằng cách nhấn vào icon   ,
               sau đó chọn một ảnh trong album kho ảnh của thiết bị.
           Nếu mã QR hợp lệ di chuyển sang bước 4
     ● Bước 4: Ứng dụng hiển thị màn hình xác nhận thông tin thanh toán.
           o Nhấn nút Thanh toán nếu tiếp tục thanh toán.


11
           o Nhấn nút Quét thêm nếu KH muốn quét thêm QR sản phẩm khác.




     ● Bước 5: Nhập vào địa chỉ giao hàng như màn hình dưới đây:




     ● Bước 6. Ứng dụng hiển thị màn hình Xác thực giao dịch.



12
        Thực hiện xác thực giao dịch và Nhấn nút Tiếp tục để hoàn tất.




     ● Bước 7. Ứng dụng hiển thị màn hình giao dịch thành công.




     3.2.3 Thanh toán QR Code cho điểm chấp nhận online
        Mục đích: Khách hàng thanh toán thành công cho đơn hàng qua chức năng QR Pay trên các
        ứng dụng Mobile Banking.
        Ví dụ: QR cho đơn hàng tại website TMĐT có kết nối qua Cổng than toán, QR cho
        đơn hàng dịch vụ trên website của đối tác/đơn vị kinh doanh có kết nối thanh toán QR
        Code Online.

        Các ứng dụng ngân hàng hỗ trợ thanh toán như: Agribank, Vietcombank, Vietinbank, BIDV,
        SCB, IVB, NCB, SHB, MSB, VIB
        Các bước thực hiện:

     ● Bước 1: KH thực hiện chọn hàng hóa, dịch vụ trên website TMĐT và nhấn thanh
       toán. Màn hình Popup Cổng thanh toán được hiển thị như sau:




13
     ▪   Bước 2:
            o KH có thể sử dụng chức năng QR Pay trên ứng dụng Mobile Banking của
              các Ngân hàng quét mã QR.
            o Trường hợp tạo đơn hàng tại website TMĐT trên trình duyệt thiết bị Mobile:
              KH có thể nhấn biểu tượng Ngân hàng như dưới đây. Sau đó đăng nhập ứng
              dụng Mobile Banking để tiếp tục thanh toán, như hướng dẫn tại bước 5.




     ▪   Bước 3:
            o Đăng nhập vào App Mobile Banking có hỗ trợ QR Code thanh toán
            o Tại màn hình Home, chọn chức năng QR Pay



14
     ● Bước 4:
            ▪   Cách 1: Thực hiện di chuyển camera đến vùng chứa mã QR đã tạo ở bước 1

            ▪   Cách 2: Chọn mã QR từ thư viện ảnh đã lưu bằng cách nhấn vào icon   ,
                sau đó chọn một ảnh trong album kho ảnh của thiết bị.
            Nếu mã QR hợp lệ di chuyển sang bước 5.
     ● Bước 5. Ứng dụng hiển thị màn hình xác nhận thông tin thanh toán.
        Nhấn nút Thanh toán




     ● Bước 6. Ứng dụng hiển thị màn hình Xác thực giao dịch.
        Thực hiện xác thực giao dịch và Nhấn nút Tiếp tục để hoàn tất.




15
      ● Bước 7. Ứng dụng hiển thị màn hình giao dịch thành công.




     4 ĐẶC TẢ KẾT NỐI GIỮA VNPAY-MERCHANT
       Phần tài liệu này là các thông tin định nghĩa giao diện để kết nối giữa VNPAY và
       Merchant bằng giao thức … cho dịch vụ thanh toán QR.
       Tất cả các giao dịch trao đổi giữa VNPAY và Merrchant đều sử dụng tiêu chuẩn …. để
       kết nối.




16
             4.1    Yêu cầu khởi tạo QRCode (CreateQRCode)

             a. Tiêu chuẩn về hình ảnh QRCode

            QR code của VNPAY sẽ có hình ảnh logo như bên dưới




            Để gen QR theo tiêu chuẩn VNPAY, sẽ dùng thông tin QRString (được mô tả ở mục 4.1b)

            và bộ source code insert khung ảnh (được mô tả ở 4.1c).

             b. Hàm createQR lấy String QR

             Thông tin môi trường test:

      STT                                   Url                                      Mô tả
        1          https://doitac-                                       Method = POST
                   tran.vnpaytest.vn/QRCreateAPIRestV2/rest/CreateQrcode Content_type = text/plain
                   Api/createQrcode

             Tham số đầu vào string json như sau:

        Tên trường             Kiểu                     Mô                     Yêu           Độ dài
                                                         tả                    cầu
     appId                    String   Được VNPAY cung cấp riêng cho         Required     Max(100)
                                       từng đối tác đi kèm nó là private
                                       Key
     merchantName             String   Tên viết tắt của Merchant             Required        Max(25)
     serviceCode              String   Mã dịch vụ QR.                        Required        Max(20)
                                       Giá trị mặc định là 03
     countryCode              String   Mã Vùng: default VN                   Required           2
17
     merchantCode    String   Mã merchant                           Required   Max(20)
     terminalId      String   Mã điểm thu                           Required    Max(8)
     payType         String   Mã dịch vụ QR.                        Required    Max(4)
                              Giá trị mặc định 03
     productId       String   Mã sản phẩm. Giá trị mặc định để      Optional   Max(20)
                              empty
     txnId           String   Mã đơn hàng, Mã GD. Required (        Optional   Max(15)
                              Dùng cho payType = 01)
     billNumber      String   Số hóa đơn                            Required   Max(20)
                              Áp dụng payType = 03
     amount          String   Số tiền                               Required   Max(13)
     ccy             String   Mã tiền tệ : Giá trị mặc định 704     Required    Max(3)
     expDate         String   Thời gian hết hạn thanh toán, định    Required   Max(14)
                              dạng: yyMMddHHmm
     desc            String   Mô tả thêm thông tin không được       Optional   Max(19)
                              quá 19 ký tự
     masterMerCode   String   Mã đơn vị phát triển merchant:        Required   Max(100)
                              default : A000000775
     merchantType    String   Mã loại hình doanh nghiệp             Required    Max(9)

     tipAndFee       String   Tiền tip and fee. Giá trị mặc         Optional   Max(20)
                              định để empty
     consumerID      String   Mã khách hàng, dành cho               Optional   Max(20)
                              payType          04
     purpose         String   Mã dịch vụ billing cho QR type        Optional   Max(19)
                              04
     checksum        String   Checksum của dữ liệu gửi. Được        Required   Max(32)
                              tính theo công thức (trong đó
                              secretKey là một mã bí mật):

                              data =EncodeMD5(appId + "|" +
                              merchantName + "|" + serviceCode
                              + "|" + countryCode + "|" +
                              masterMerCode + "|" +
                              merchantType + "|" +
                              merchantCode + "|" + terminalId
                              + "|" + payType + "|" + productId +
                              "|" + txnId + "|" + amount + "|" +
                              tipAndFee + "|" + ccy + "|" +
                              expDate + "|" + secretKey)

                              Chú ý :

                                 ✔ secretKey = Sẽ cung riêng

18
                                         cho từng đối tác
                                       ✔ appId = Sẽ cung riêng
                                         cho từng đối tác



           Request example:

           {
                 "appId": "MERCHANT",
                 "merchantName": "VNPAY TEST",
                 "serviceCode": "03",
                 "countryCode": "VN",
                 "masterMerCode": "A000000775",
                 "merchantType": "9999",
                 "merchantCode": "********",
                 "payloadFormat": "",
                 "terminalId": "PSSTEST",
                 "payType": "03",
                 "productId": "",
                 "txnId": "VNP_TEST888",
                 "amount": "100000",
                 "tipAndFee": "",
                 "ccy": "704",
                 "expDate": "",
                 "desc": "",
                 "checksum": "A8833240FA23EE9FA5D5D081EA8A7540",
                 "mobile": "",
                 "billNumber": "VNP_TEST888",
                 "consumerID": "",
                 "purpose": ""
           }

           Dữ liệu trả về dạng json với thông tin như sau:

       Tên trường         Kiểu                    Mô tả                   Yêu cầu      Độ dài
     code               String    Mã lỗi trả về (Bảng mã lỗi)           Required    Max (20)
     message            String    Mô tả mã lỗi chi đính kèm(Bảng mã Required        Max (100)
                                  lỗi)
     data               String    Dữ liệu qrcode trả về             Optional        Free
     url                String    url trả về, default: null             Optional    Free
     checksum           String    Checksum được tính theo công thức     Required    Max (32)

                                  sau:
                                  data = code + "|" + message + "|" +
                                  data + "|" + url + "|" + secretKey;

                                  Chú ý :
19
                                     checksum = md5(data);
                                     secretKey= Sẽ cung riêng cho từng
                                     đối tác

         Response Example:
     {
         "code":"00",
         "message":"Success",
         "data":"00020101021226260010A000000775010********852049999530370454061000005
         802VN5910VNPAY
         TEST6005HANOI62330111VNP_TEST8880303PSS0707PSSTEST63046DF7",
         "url":null,
         "checksum":"732BEFB12DEF461C90E9E4588538C5D9",
         "isDelete":true,
         "idQrcode":"7178242194757656576"
     }

         Bảng mã lỗi:

            Số TT            Mã Lỗi                           Mô tả mã lỗi
            1                   00           Success.
            2                   01           Data input is not in format
            3                   04           Insert data QrCode failed
            4                   05           Ip is denied
            5                   06           False checkSum
            6                   07           Merchant is not exist
            7                   09           Service code is invalid
            8                   10           AppId is invalid
            9                   11           Merchant is not active
            10                  12           Master merchant code is null or empty
            11                  15           ConsumerID is null or empty
            12                  16           Purpose is null or empty
            13                  21           Terminal is invalid
            14                  24           Terminal is inactive
            15                  99           Internal errors
            16                  96           System is maintaining


         c. Code mẫu insert khung ảnh QRCode VNPAY

         Col span={18} offset={3} className="mb-2 container-qrcode pd-10-percent text-center" style={{
         backgroundImage: `url(${urlQrBg})` }}>
                  <div id="qrcode" />
                 </Col>


         c.1, Code html

20
     .container-qrcode {
         background-position: center;
         background-repeat: no-repeat;
         background-size: contain;
     }


     .pd-10-percent {
         padding: 10%;
     }


     .text-center {
         text-align: center;
     }


     c.2, Code css


     if (localStorage.getItem('qrItemTerminal')) {
           const qrItemTerminal = JSON.parse(localStorage.getItem('qrItemTerminal'));
           getQrItemTerminal({
              id: (qrItemTerminal && qrItemTerminal.id) || 0,
              createDate: (qrItemTerminal && qrItemTerminal.createDate) || '',
             }).then((res) => {
               if (res && res.code === '00') {
                 setQrCodeDetail(res.data ? res.data : []);
                 const options = {
                  text: res && res.data && res.data.qrData,
                  width: 200,
                  height: 200,
                  correctLevel: QRCode.CorrectLevel.M,
                  PO_TL: '#005AAB',
                  PI_TL: '#005AAB',
                  PO_TR: '#005AAB',
                  PI_TR: '#005AAB',
                  PO_BL: '#C9181E',
                  PI_BL: '#C9181E',
                 };
                 new QRCode(document.getElementById('qrcode'), options);

21
                           new QRCode(document.getElementById('qr-download'), options);
                           new QRCode(qrcodePrint.current, options);
                       } else {
                           notification.destroy();
                           notification.error({
                            message: 'Thông báo',
                            description: 'Có lỗi trong quá trình xử lí, vui lòng thử lại sau!',
                           });
                       }
                     });
                 }

               4.2 Yêu cầu cập nhật trạng thái thanh toán (Merchant Payment)
               Yêu cầu merchant xây dựng api RESTful với tham số đầu vào là chuỗi Json như sau.

               Tham số đầu vào:

 Tên trường                Kiểu                   Mô tả                                    Yêu Cầu    Độ Dài
 code                      String                 Mã Lỗi phần trừ tiền khách hàng          Required   Max(10)
 message                   String                 Mô tả mã lỗi trừ tiền khách hàng         Required   Max(100)
 msgType                   String                  Loại thanh                              Required   Max(10)

                                                   toán 1: Thanh

                                                   toán

                                                  2: Đặt hàng
 txnId                     String                 Mã đơn hàng, Số hóa đơn trong            Required   Max(20)
                                                  QRCode
 qrTrace                   String                 Số trace giao dịch : không được          Required   Max(10)
                                                  trùng nhau
 bankCode                  String                 Mã ngân hàng thanh toán                  Required   Max(10)
 mobile                    String                 Số điện thoại khách hàng                 Optional   Max(20)
 accountNo                 String                 Số tài khoản                             Optional   Max(30)
 amount                    String                 Số tiền thanh toán                       Required   Max(13)
 payDate                   String                 Thời hạn thanh toán                      Required   Max(14)
 merchantCode              String                 Mã code quy định cho VNPAY.              Required   Max(20)
 terminalId                String                 Mã terminal                              Required   Max(8)
 name                      String                 Tên người nhận hàng. Tối đa              Optional   Max(100)
                                                  không quá 255 ký tự
 phone                     String                 Số điện thoại nhận hàng (Số này          Optional   Max(20)
                                                  có thể giống hoặc khác với mobile
                                                  bên trên)
 province_id               String                 ID của tỉnh nhận hàng (Theo              Optional   Max(14)
                                                  thông tin từ VnShop)
 district_id               String                 ID của quận/huyện nhận hàng              Optional   Max(14)
                                                  (Theo thông tin từ VnShop)
 address                   String                 Địa chỉ nhận hàng                        Optional   Max(100)
 email                     String                 Địa chỉ email                            Optional   Max(100)
22
 addData           String              Json QrCodeItemPayment bao       Optional   Free
                                       gồm các trường sau:

                                       QrCodeItemPayment:

                                            -   String:
                                                productId;//Ma san
                                                pham
                                            -   String: amount;
                                                //Don gia
                                            -   String:
                                                tipAndFee;//Tie
                                                n Tip/Phi
                                            -   String: ccy; //Ma
                                                tien te
                                             - String: qty;//So luong
                                      String :note;//Ghi chu
 checksum          String              Dữ liệu mã hóa MD5 của chuỗi     Required   Max(32)
                                       sau:
                                       code + "|" + msgType + "|" +
                                       txnId + "|" + qrTrace + "|" +
                                       bankCode + "|" + mobile + "|"
                                       + accountNo + "|" + amount +
                                       "|"
                                       + payDate + "|" +
                                       merchantCode + "|" +
                                       secretKey
                                      Với secretKey sẽ do bên
                                      Merchant cung cấp cho VNPAY


            Request to merchant example:

            {

                "code": "00",

                "message": "Tru tien thanh cong, so trace 100550",

                "msgType": "1",

                "txnId": "50141",

                "qrTrace": "*********",

                "bankCode": "VIETCOMBANK",

                "mobile": "**********",

                "accountNo": "",
23
         "amount": "1000000",

         "payDate": "**************",

         "masterMerCode": "A000000775",

         "merchantCode": "**********",

         "terminalId": "FPT02",

         "addData": [{

                "merchantType": "5045",

                "serviceCode": "06",

                "masterMerCode": "A000000775",

                "merchantCode": "**********",

                "terminalId": "FPT02",

                "productId": "",

                "amount": "100000",

                "ccy": "704",

                "qty": "1",

                "note": ""

         }],

         "checksum": "81F77683FEA4EBE2CE748AFC99CC3AE9",

         "ccy": "704",

         "secretKey": "VNPAY"

     }

     Kết quả trả về cho VNPAY dạng json như sau:

         Tên trường                Kiểu     Mô tả        Yêu cầu     Độ dài
         code                      String   Mã lỗi trả về Required   Max(20)

         message                   String   Mô tả mã lỗi Required    Max(100)
                                            đính kèm




24
              data                      Json         Chi tiết lỗi –   Optional        Free
                                                     Không bắt
                                                     buộc


          Response from merchant example:

          {

              "code": "00",

              "message": "đặt hàng thành công",

              "data": {

                     "txnId": "50141"
              }
          }

          Bảng mô tả mã lỗi trả về cho VNPAY:


 Mã lỗi   Kiểu dữ liệu          Mô tả mã lỗi
 01       String                 Thiếu hàng trong đơn hàng
                                 Thông tin message sẽ hiển thị dạng List Json các sản phẩm thiếu <Products> với
                                 nội dung cụ thể bao gồm:

                                 Data{
                                         -   String: productid;// Mã sản phẩm
                                         -   String: qty;// Số lượng còn trong kho
                                }
 02       String                Hết toàn bộ hàng trong đơn hàng
                                Thông tin message sẽ hiển thị dạng String : “Kho hàng đã hết sản phẩm”
 03       String                 Đơn hàng đã được thanh toán
                                 Thông tin message sẽ hiển thị dạng String: “Đơn hàng đã được thanh toán”.
                                 Data{
                                       - String: txnId; // Mã đơn hàng
                                }
 04       String                Lỗi tạo đơn hàng
                                Thông tin message sẽ hiển thị dạng String mô tả mã lỗi exception.
 05       String                 Thông tin message sẽ hiển thị dạng String : “đơn hàng đang được xử lí”

                                 Data{
                                         -   String: txnId; // Mã đơn hàng
                                }
 06       String                Thông tin message sẽ hiển thị dạng String : “sai thông tin xác thực”
 07       String                 Thông tin message sẽ hiển thị dạng String : “số tiền không chính xác”

                                 Data{
                                         -   String: amount; // Số tiền chính xác của đơn hàng
                                }
25
 08             String               Giao dịch timout
 09             String               QR hết hạn thanh toán
 10             String               IP không được truy cập
 00             String                Thông tin message sẽ hiển thị dạng String : “đặt hàng thành công”

                                      Data{
                                              -   String: txnId; // Mã đơn hàng
                                     }


             4.3 Yêu cầu kiểm tra giao dịch (CheckTrans)
          Thông tin môi trường test:

      Số TT                                        Url                                           Mô tả
            1       https://doitac-                                                       Method = POST
                    tran.vnpaytest.vn/CheckTransaction/rest/api/CheckTrans                Content_type =
                                                                                          application/json


                a. Tham số đầu vào
      Stt               Tên Trường         Kiểu                                   Mô tả

                txnId                    String       Số hóa đơn đối với Qr Terminal động
      1
                merchantCode             String       Mã của merchant
      2
                terminalID               String       Mã của terminal
      3
                payDate                  String       Thời gian giao dịch của bank truyền về cho hệ thống
      4
                                                      MMS, định dạng “dd/MM/yyyy”

      5
                checkSum                 String       Checksum của dữ liệu gửi. Được tính theo công thức
                                                      (trong đó secretKey là một mã bí mật):

                                                      data =EncodeMD5(payDate+ "|" + txnId+ "|" +
                                                      merchantCode+ "|" + terminalID+ "|" + secretKey)

                                                      Chú ý :

                                                          ✔ secretKey = Sẽ cung riêng cho từng đối tác

          Request Example:
             {
              "merchantCode":"********",
              "checkSum":"8417a925dfee935744c57cb3340030ef",
              "terminalID":"PSSTEST",
              "txnId":"SCSREWITSP",
              "payDate":"27/03/2024"
             }

26
           b. Dữ liệu trả về

     STT             Trường      Kiểu                             Mô tả

           code                 String   Mã lỗi trả về
     1
           message              String   Mô tả mã lỗi đính kèm
     2
           masterMerchantCode   String   Tên master merchant
     3
           merchantCode         String   Định danh merchant
     4
           terminalID           String   Định danh terminal
     5
           billNumber           String   Số hóa đơn
     6
           txnId                String   Số hóa đơn
     7
           payDate              String   Thời gian thanh toán
     8
           qrTrace              String   Số trace VNPAY
     9
           bankCode             String   Ngân hàng thanh toán
     10
           debitAmount          String   Số tiền trước KM
     11
           realAmount           String   Số tiền sau khuyến mãi
     12

     13
           checkSum             String   Checksum của dữ liệu gửi. Được tính theo công
                                         thức (trong đó secretKey là một mã bí mật):
                                         data =EncodeMD5(MasterMerchantCode+ "|" +
                                         MerchantCode+ "|" + TerminalID + "|" + TxnId+
                                         "|"+ PayDate+ "|" + BankCode+ "|" + QrTrace+ "|"
                                         + DebitAmount+ "|" + RealAmount+ "|" +
                                         secretKey)

                                         Chú ý:
                                             ✔ secretKey = Sẽ cung riêng cho từng đối tác

           Response to merchant:
                {
                   "code": "00",
                   "message": "Giao dich thanh cong.",
                   "masterMerchantCode": "A000000775",
                   "merchantCode": "********",
                   "terminalID": "PSSTEST",
                   "billNumber": "SCSREWITSP",
                   "txnId": "SCSREWITSP",
                   "payDate": "27/03/2024 13:56:24",
                   "qrTrace": "*********",
                   "bankCode": "VNPAYEWALLET",
                   "debitAmount": "100000",
                   "realAmount": "100000",
                   "checkSum": "3F907FC92DE68728EBD11A826877628F"
27
                  }


           c. Bảng danh sách mã lỗi

            Giá trị trả về      Kiểu dữ liệu                                Mô tả
                 00                   String         Giao dịch thành công

                 01                   String         Không tìm thấy giao dịch

                 02                   String         PayDate không đúng định dạng.

                 03                   String         TxnId không được null hoặc empty.

                 04                   String         Giao dich thất bại.

                 08                   String         Giao dich nghi vấn.

                 14                   String         IP bị khóa.

                 11                   String         Dữ liệu đầu vào không đúng định dạng.

                 99                   String         Internal error


           4.4   Yêu cầu hoàn tiền (MerchantRefund)

           Địa chỉ webservice
     Stt                                       Url                                        Mô tả
                                                                                    Method = POST
     1      https://doitac-tran.vnpaytest.vn/mms/refund
                                                                                    Content_type =
                                                                                    application/json

           a. Tham số đầu vào

              Tên trường                 Kiểu                                   Mô tả

     merchantCode               String               Mã merchant

     qrTrace                    String               Số trace QR

     payTxnId                   String               Số hóa đơn

     refundTxnId                Long                 Số trace của hệ thống merchant
                                                     Loại hoàn tiền
     typeRefund                 String               1 là hoàn toàn phần
                                                     2 là hoàn một phần
     amount                     int                  Số tiền hoàn

     refundContent              String               Nội dung hoàn tiền

28
                                               Thời gian ghi nhận giao dịch thanh toán
     payDate                   String
                                               (YYYYmmDDhhMMSS)
                                               Mã hóa MD5 chuỗi (sercretKey + merchantCode+
     checkSum                  String          qrTrace+ payTxnId + refundTnxId +
                                               typeRefund+ amount+ payDate)

     Request Example:

        {
                "merchantCode":"0315275368A",

                "amount":"147000",

                "refundTxnId":"575214",

                "typeRefund":"1",

                "qrTrace":"244202482",

                "checkSum":"3d799685c7d315c748ddceedc17b94b3",

                "refundContent":"",

                "payTxnId":"679363",

                "payDate":"20210721153311"

        }

        b. Dữ liệu trả về


            Tên trường                  Kiểu                           Mô tả

     resCode                   String          Mã lỗi trả về

     resDesc                   String          Mô tả chi tiết về lỗi

     qrTraceRefund             String          Số trace hoàn tiền giao dịch

     refundDate                Long            Thời gian hoàn tiền
                                               MD5(resCode+ resDesc+ qrTraceRefund+
     checkSum                  String
                                               refundDate)

        Response to merchant:

        {
        "code":"00","message":"Refund Success.","qrTraceRefund":"000000521","refundDate":
        "20170505093009","checkSum":"AB5ED43B77FCA16D6A0DE797BE1FA9D9"
       }
        c. Các mã lỗi trả về
29
                         Kiểu dữ
      Giá trị trả về                                         Mô tả
                           liệu

     00                String      Refund Success
     01                String      Checksum is wrong.
     02                String      Money is invalid - a part
     03                String      Money is invalid - totality
     04                String      Not allow refund totality after refund a part
     11                String      Format data is wrong
     12                String      Transaction not found
     14                String      IP is denied
     96                String      System is maintaing
     99                String      Internal error




30
